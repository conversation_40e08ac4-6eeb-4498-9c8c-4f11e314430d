<template>
    <div class="main-periodCompareDataBoard-container">
        <Card class="main-periodCompareDataBoard-card">
            <template #header>
                <div class="card-header">
                    <div class="left">
                        <span class="card-title" style="z-index: 10;">周期业务数据明细</span>
                    </div>
                    <div class="right">
                        <Select v-model="selectedPeriod" :options="Period" optionLabel="name" placeholder="快捷区间查询"
                            class="w-full md:w-56" size="small" />

                        <FloatLabel variant="on">
                            <DatePicker v-model="Range1" inputId="start_date" showIcon iconDisplay="input" size="small"
                                :disabled="isDatePickerDisabled" />
                            <label for="start_date">开始时间</label>
                        </FloatLabel>
                        <FloatLabel variant="on">
                            <DatePicker v-model="Range2" inputId="end_date" showIcon iconDisplay="input" size="small"
                                :disabled="isDatePickerDisabled" />
                            <label for="end_date">结束时间</label>
                        </FloatLabel>
                        <Button class="download-button" icon="pi pi-undo" style="font-size: 1rem;" size="small"
                            @click="resetAllSelections" />
                        <Button class="download-button" icon="pi pi-search" style="font-size: 1rem;" size="small" />
                    </div>

                </div>
            </template>
            <template #content>
                <TreeTable :value="nodes" tableStyle="min-width: 50rem" stripedRows scrollHeight="27rem">
                    <Column header="业务人员" expander field="name" footer="总计:" footerStyle="text-align: left" />
                    <Column header="新增客户数" sortable field="addCustomer" :footer="Sum_addCustomer"
                        footerStyle="text-align: left；font-weight: bold;" />
                    <Column header="总客户数" sortable field="totalCustomer" :footer="Sum_totalCustomer"
                        footerStyle="text-align: left；font-weight: bold;" />
                    <Column header="新增权益（仅入金）" sortable field="addEquity" :footer="Sum_addEquity"
                        footerStyle="text-align: left；font-weight: bold;" />
                    <Column header="新增净权益（入金-出金）" sortable field="addNetEquity" :footer="Sum_addNetEquity"
                        footerStyle="text-align: left；font-weight: bold;" />
                    <Column header="总权益" sortable field="totalEquity" :footer="Sum_totalEquity"
                        footerStyle="text-align: left；font-weight: bold;" />
                    <Column header="新增手续费" sortable field="addFee" :footer="Sum_addFee"
                        footerStyle="text-align: left；font-weight: bold;" />
                    <Column header="总手续费" sortable field="totalFee" :footer="Sum_totalFee"
                        footerStyle="text-align: left；font-weight: bold;" />
                </TreeTable>
            </template>

        </Card>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import TreeTable from 'primevue/treetable';
import Card from 'primevue/card';
import FloatLabel from 'primevue/floatlabel';
import DatePicker from 'primevue/datepicker';
import Button from 'primevue/button';
import Select from 'primevue/select';
import Column from 'primevue/column';



// 日期选择器变量
const Range1 = ref();
const Range2 = ref();

//模拟数据-快捷区间选项
const selectedPeriod = ref();
const Period = ref([
    { name: '近1周', code: 'NY' },
    { name: '近1月', code: 'RM' },
    { name: '近3月', code: 'LDN' },
]);

// 计算属性：判断日期选择器是否应该被禁用
const isDatePickerDisabled = computed(() => {
    return selectedPeriod.value !== null && selectedPeriod.value !== undefined;
});

// 监听快捷区间选择的变化
watch(selectedPeriod, (newValue) => {
    if (newValue) {
        // 当选择了快捷区间时，重置日期选择器
        Range1.value = null;
        Range2.value = null;
    }
});

// 重置所有选择的函数
const resetAllSelections = () => {
    selectedPeriod.value = null;
    Range1.value = null;
    Range2.value = null;
};


// 聚合计算方法
const getAddCustomerCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.addCustomer || 0), 0);
};

const getTotalCustomerCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.totalCustomer || 0), 0);
};

const getAddEquityCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.addEquity || 0), 0);
};

const getAddNetEquityCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.addNetEquity || 0), 0);
    // parseFloat(sum.toFixed(2))
};

const getTotalEquityCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.totalEquity || 0), 0);
};

const getTotalAddFeeCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.addFee || 0), 0);
};

const getTotalFeeCount = (children) => {
    if (!children || children.length === 0) return 0;
    return children.reduce((sum, child) => sum + (child.data.totalFee || 0), 0);
};

// 计算并更新聚合数据的函数
const calculateAggregateData = (data) => {
    return data.map(node => {
        if (node.children && node.children.length > 0) {
            // 递归计算子节点的聚合数据
            const updatedChildren = calculateAggregateData(node.children);

            // 计算当前节点的聚合数据
            const aggregatedData = {
                ...node.data,
                addCustomer: parseFloat(getAddCustomerCount(updatedChildren).toFixed(2)),
                totalCustomer: parseFloat(getTotalCustomerCount(updatedChildren).toFixed(2)),
                addEquity: parseFloat(getAddEquityCount(updatedChildren).toFixed(2)),
                addNetEquity: parseFloat(getAddNetEquityCount(updatedChildren).toFixed(2)),
                totalEquity: parseFloat(getTotalEquityCount(updatedChildren).toFixed(2)),
                addFee: parseFloat(getTotalAddFeeCount(updatedChildren).toFixed(2)),
                totalFee: parseFloat(getTotalFeeCount(updatedChildren).toFixed(2))
            };

            return {
                ...node,
                data: aggregatedData,
                children: updatedChildren
            };
        }
        return node;
    });
};

// 静态模拟数据
const mockTreeData = [
    {
        key: '0',
        data: {
            name: '曹乐',
            addCustomer: 10,
            totalCustomer: 108,
            addEquity: -1592611.99,
            addNetEquity: 6685099.85,
            totalEquity: 7885099.85,
            addFee: 680075.23,
            totalFee: 1310075.23
        }
    },
    {
        key: '1',
        data: {
            name: '机构业务部',
            addCustomer: 0, // 将在计算后更新
            totalCustomer: 0, // 将在计算后更新
            addEquity: 0, // 将在计算后更新
            addNetEquity: 0, // 将在计算后更新
            totalEquity: 0, // 将在计算后更新
            addFee: 0, // 将在计算后更新
            totalFee: 0 // 将在计算后更新
        },
        children: [
            {
                key: '1-0',
                data: {
                    name: '陆健',
                    addCustomer: 6,
                    totalCustomer: 86,
                    addEquity: 987245.32,
                    addNetEquity: 4123560.78,
                    totalEquity: 5210806.10,
                    addFee: 421560.35,
                    totalFee: 832750.67
                }
            },
            {
                key: '1-1',
                data: {
                    name: '金胜尧',
                    addCustomer: 26,
                    totalCustomer: 156,
                    addEquity: 2156890.45,
                    addNetEquity: 9236540.21,
                    totalEquity: 11393430.66,
                    addFee: 942870.56,
                    totalFee: 1856320.89
                }
            },
            {
                key: '1-2',
                data: {
                    name: '王子淳',
                    addCustomer: 7,
                    totalCustomer: 95,
                    addEquity: -876540.23,
                    addNetEquity: 3689210.75,
                    totalEquity: 4565750.98,
                    addFee: 378540.62,
                    totalFee: 742150.38
                }
            }
        ]
    },
    {
        key: '2',
        data: {
            name: '经纪业务部',
            addCustomer: 0, // 将在计算后更新
            totalCustomer: 0, // 将在计算后更新
            addEquity: 0, // 将在计算后更新
            addNetEquity: 0, // 将在计算后更新
            totalEquity: 0, // 将在计算后更新
            addFee: 0, // 将在计算后更新
            totalFee: 0 // 将在计算后更新
        },
        children: [
            {
                key: '2-0',
                data: {
                    name: '温贵忠',
                    addCustomer: 13,
                    totalCustomer: 120,
                    addEquity: -1356890.76,
                    addNetEquity: 5689210.34,
                    totalEquity: 7046101.10,
                    addFee: 582140.75,
                    totalFee: 1143680.92
                }
            },
        ]
    },
    {
        key: '3',
        data: {
            name: '马帅帅',
            addCustomer: 2,
            totalCustomer: 68,
            addEquity: -456210.89,
            addNetEquity: 1897650.32,
            totalEquity: 2353861.21,
            addFee: 195320.68,
            totalFee: 382650.45
        }
    },
    {
        key: '4',
        data: {
            name: '邹军',
            addCustomer: 9,
            totalCustomer: 102,
            addEquity: 1245780.65,
            addNetEquity: 5236890.47,
            totalEquity: 6482671.12,
            addFee: 536240.89,
            totalFee: 1053680.74
        }
    },

    {
        key: '5',
        data: {
            name: '潘超',
            addCustomer: 23,
            totalCustomer: 145,
            addEquity: 1987650.43,
            addNetEquity: 8326540.91,
            totalEquity: 10314191.34,
            addFee: 853260.47,
            totalFee: 1678420.63
        }
    },
];

onMounted(() => {
    // 模拟异步加载数据
    setTimeout(() => {
        // 计算聚合数据后再赋值
        nodes.value = calculateAggregateData(mockTreeData);
    }, 100);
});

const nodes = ref();

// 总计计算
const Sum_addCustomer = computed(() => {
    if (!nodes.value) return 0;
    return nodes.value.reduce((sum, node) => sum + (node.data.addCustomer || 0), 0);
});

const Sum_totalCustomer = computed(() => {
    if (!nodes.value) return 0;
    return nodes.value.reduce((sum, node) => sum + (node.data.totalCustomer || 0), 0);
});

const Sum_addEquity = computed(() => {
    if (!nodes.value) return 0;
    return parseFloat(nodes.value.reduce((sum, node) => sum + (node.data.addEquity || 0), 0).toFixed(2));
});

const Sum_addNetEquity = computed(() => {
    if (!nodes.value) return 0;
    return parseFloat(nodes.value.reduce((sum, node) => sum + (node.data.addNetEquity || 0), 0).toFixed(2));
});

const Sum_totalEquity = computed(() => {
    if (!nodes.value) return 0;
    return parseFloat(nodes.value.reduce((sum, node) => sum + (node.data.totalEquity || 0), 0).toFixed(2));
});

const Sum_addFee = computed(() => {
    if (!nodes.value) return 0;
    return parseFloat(nodes.value.reduce((sum, node) => sum + (node.data.addFee || 0), 0).toFixed(2));
});

const Sum_totalFee = computed(() => {
    if (!nodes.value) return 0;
    return parseFloat(nodes.value.reduce((sum, node) => sum + (node.data.totalFee || 0), 0).toFixed(2));
});
</script>

<style >
@layer reset, primevue, custom;

@layer custom {
    .main-periodCompareDataBoard-container {
        /* background-color: bisque; */
        /* height: 800px; */
    }

    .main-periodCompareDataBoard-card {
        border: #6a6b6e;
    }



    .main-periodCompareDataBoard-container .card-header {
        display: flex;
        flex-direction: row;
        align-items: center;

        position: relative;

        width: 100%;
    }

    .main-periodCompareDataBoard-container .card-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('@/assets/coolbackgrounds-topography-micron.svg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        transform: scaleX(-1);
        z-index: 0;
    }

    .main-periodCompareDataBoard-container .card-header .left {
        /* background-color: antiquewhite; */

        width: 40%;

        font-size: 1.3rem;
        font-weight: bold;
        letter-spacing: 0.08rem;

        padding: 0.4rem;
        padding-left: 0.7rem;
    }

    .main-periodCompareDataBoard-container .card-header .right {
        /* background-color: rgb(209, 165, 106); */

        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 0.8rem;

        width: 60%;

        padding: 0.4rem;
    }

    .main-periodCompareDataBoard-container .card-header .right .download-button {
        background-color: #202020;
        border: none;
    }

    /* TreeTable footer 加粗样式 */
    .p-treetable .p-treetable-tfoot td {
        font-weight: bold !important;
    }
}
</style>

