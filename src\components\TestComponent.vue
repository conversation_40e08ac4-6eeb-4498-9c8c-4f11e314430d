<template>
    <div class="main-periodCompareDataBoard-container">
        <Card>
            <template #header>
                <div class="card-header">
                    <div class="left">
                        <span class="card-title" style="z-index: 10;">周期业务数据对比</span>
                    </div>
                    <div class="right">
                        <FloatLabel variant="on">
                            <DatePicker v-model="Range1" inputId="on_label" showIcon iconDisplay="input"
                                selectionMode="range" :manualInput="false" size="small" />
                            <label for="on_label">周期1</label>
                        </FloatLabel>
                        <FloatLabel variant="on">
                            <DatePicker v-model="Range2" inputId="on_label" showIcon iconDisplay="input"
                                selectionMode="range" :manualInput="false" size="small" />
                            <label for="on_label">周期2</label>
                        </FloatLabel>
                        <Button class="download-button" icon="pi pi-undo" style="font-size: 1rem;" size="small"
                            @click="resetAllSelections" />
                        <Button class="download-button" icon="pi pi-search" style="font-size: 1rem;" size="small" />
                    </div>

                </div>
            </template>
            <template #content>
                <Splitter style="height: 27rem">
                    <SplitterPanel class="options-panel" :size="25" :minSize="10">
                        <div class="info">
                            <p>请选择要查看的业务数据项</p>
                        </div>
                        <div class="options-panel-content">
                            <Listbox v-model="selectedCity" :options="cities" optionLabel="name" class="listbox" />
                        </div>

                    </SplitterPanel>
                    <SplitterPanel class="data-panel" :size="75">


                        <TreeTable :value="nodes" tableStyle="min-width: 50rem" stripedRows scrollHeight="27rem">
                            <Row>
                                <Column header="业务人员" expander field="name" :rowspan="3" footer="总计:"
                                    footerStyle="text-align: left" />
                            </Row>
                            <Row>
                                <Column :header="selectedCity.name" :colspan="4" />
                            </Row>
                            <Row>
                                <Column header="周期1" sortable field="lastYearSale" :footer="lastYearTotal"
                                    footerStyle="text-align: left；font-weight: bold;" />
                                <Column header="周期2" sortable field="thisYearSale" :footer="thisYearTotal"
                                    footerStyle="text-align: left；font-weight: bold;" />
                                <Column header="差值（周期1-周期2）" sortable field="diff" :footer="diffTotal"
                                    footerStyle="text-align: left; font-weight: bold;">
                                    <template #body="slotProps">
                                        <span :class="getDiffColumnColor(slotProps.data.diff)">
                                            {{ slotProps.data.diff }}
                                        </span>
                                    </template>
                                </Column>
                            </Row>

                        </TreeTable>


                    </SplitterPanel>
                </Splitter>

            </template>
        </Card>
    </div>

</template>
<script setup>
import Card from 'primevue/card';
import FloatLabel from 'primevue/floatlabel';
import DatePicker from 'primevue/datepicker';
import Button from 'primevue/button';

import Splitter from 'primevue/splitter';
import SplitterPanel from 'primevue/splitterpanel';


import Column from 'primevue/column';

import Row from 'primevue/row';


import Listbox from 'primevue/listbox';

import { ref, computed } from "vue";

// 日期选择器变量
const Range1 = ref();
const Range2 = ref();

//模拟数据-周期对比面板的业务数据选项
const selectedCity = ref({ name: '新增客户数', code: 'ACUS' });
const cities = ref([
    { name: '新增客户数', code: 'ACUS' },
    { name: '新增权益（仅入金）', code: 'APROF' },
    { name: '新增净权益（入金-出金）', code: 'ANPROF' },
    { name: '新增手续费', code: 'AST' }
]);



const lastYearTotal = computed(() => {
    let total = 0;
    for (let sale of optionData.value) {
        total += sale.lastYearSale;
    }

    return parseFloat(total.toFixed(2));
});

const thisYearTotal = computed(() => {
    let total = 0;
    for (let sale of optionData.value) {
        total += sale.thisYearSale;
    }

    return parseFloat(total.toFixed(2));
});

const diffTotal = computed(() => {
    let total = 0;
    for (let sale of optionData.value) {
        total += sale.diff;
    }

    return parseFloat(total.toFixed(2));
});

// 根据差值返回对应的样式类名
const getDiffColumnColor = (diffValue) => {
    return diffValue < 0 ? 'red-text' : 'green-text';
};

// 重置所有选择的函数
const resetAllSelections = () => {
    Range1.value = null;
    Range2.value = null;
};


// 计算并更新聚合数据的函数
const calculateAggregateData = (data) => {
    return data.map(node => {
        if (node.children && node.children.length > 0) {
            // 递归计算子节点的聚合数据
            const updatedChildren = calculateAggregateData(node.children);

            // 计算当前节点的聚合数据
            const aggregatedData = {
                ...node.data,
                addCustomer: parseFloat(getAddCustomerCount(updatedChildren).toFixed(2)),
                totalCustomer: parseFloat(getTotalCustomerCount(updatedChildren).toFixed(2)),
                addEquity: parseFloat(getAddEquityCount(updatedChildren).toFixed(2)),
                addNetEquity: parseFloat(getAddNetEquityCount(updatedChildren).toFixed(2)),
                totalEquity: parseFloat(getTotalEquityCount(updatedChildren).toFixed(2)),
                addFee: parseFloat(getTotalAddFeeCount(updatedChildren).toFixed(2)),
                totalFee: parseFloat(getTotalFeeCount(updatedChildren).toFixed(2))
            };

            return {
                ...node,
                data: aggregatedData,
                children: updatedChildren
            };
        }
        return node;
    });
};


//根据不同的选择项来展示对应的模拟数据
const optionData = computed(() => {

    let optionData = [];
    if (selectedCity.value?.code === 'ACUS') {
        optionData = [
            {
                key: '0',
                data: {
                    name: '曹乐',
                    lastYearSale: 10,
                    thisYearSale: 17,
                    diff: 10 - 17
                }
            },
            {
                key: '1',
                data: {
                    name: '机构业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '1-0',
                        data: {
                            name: '陆健',
                            lastYearSale: 5,
                            thisYearSale: 10,
                            diff: 5 - 10
                        }
                    },
                    {
                        key: '1-1',
                        data: {
                            name: '金胜尧',
                            lastYearSale: 38,
                            thisYearSale: 5,
                            diff: 38 - 5
                        }
                    },
                    {
                        key: '1-2',
                        data: {
                            name: '王子淳',
                            lastYearSale: 7,
                            thisYearSale: 7,
                            diff: 7 - 7
                        }
                    }
                ]
            },
            {
                key: '2',
                data: {
                    name: '经纪业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '2-0',
                        data: {
                            name: '温贵忠',
                            lastYearSale: 17,
                            thisYearSale: 10,
                            diff: 17 - 10
                        }
                    },
                ]
            },
            {
                key: '3',
                data: {
                    name: '马帅帅',
                    lastYearSale: 29,
                    thisYearSale: 33,
                    diff: 29 - 33
                }
            },
            {
                key: '4',
                data: {
                    name: '邹军',
                    lastYearSale: 10,
                    thisYearSale: 7,
                    diff: 10 - 7
                }
            },

            {
                key: '5',
                data: {
                    name: '潘超',
                    lastYearSale: 27,
                    thisYearSale: 33,
                    diff: 27 - 33
                }
            },
        ];
    } else if (selectedCity.value?.code === 'APROF') {
        optionData = [
            {
                key: '0',
                data: {
                    name: '曹乐',
                    lastYearSale: 12500.50,
                    thisYearSale: 17800.80,
                    diff: parseFloat((12500.50 - 17800.80).toFixed(2))
                }
            },
            {
                key: '1',
                data: {
                    name: '机构业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '1-0',
                        data: {
                            name: '陆健',
                            lastYearSale: 5200.20,
                            thisYearSale: 10350.35,
                            diff: parseFloat((5200.20 - 10350.35).toFixed(2))
                        }
                    },
                    {
                        key: '1-1',
                        data: {
                            name: '金胜尧',
                            lastYearSale: 38600.60,
                            thisYearSale: 5750.75,
                            diff: parseFloat((38600.60 - 5750.75).toFixed(2))
                        }
                    },
                    {
                        key: '1-2',
                        data: {
                            name: '王子淳',
                            lastYearSale: 71000.10,
                            thisYearSale: 71000.10,
                            diff: parseFloat((71000.10 - 71000.10).toFixed(2))
                        }
                    }
                ]
            },
            {
                key: '2',
                data: {
                    name: '经纪业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '2-0',
                        data: {
                            name: '温贵忠',
                            lastYearSale: 17900.90,
                            thisYearSale: 10250.25,
                            diff: parseFloat((17900.90 - 10250.25).toFixed(2))
                        }
                    },
                ]
            },
            {
                key: '3',
                data: {
                    name: '马帅帅',
                    lastYearSale: 294000.40,
                    thisYearSale: 336500.65,
                    diff: parseFloat((294000.40 - 336500.65).toFixed(2))
                }
            },
            {
                key: '4',
                data: {
                    name: '邹军',
                    lastYearSale: 10800.80,
                    thisYearSale: 7500.50,
                    diff: parseFloat((10800.80 - 7500.50).toFixed(2))
                }
            },

            {
                key: '5',
                data: {
                    name: '潘超',
                    lastYearSale: 27300.30,
                    thisYearSale: 33900.90,
                    diff: parseFloat((27300.30 - 33900.90).toFixed(2))
                }
            },
        ];
    } else if (selectedCity.value?.code === 'ANPROF') {
        optionData = [
            {
                key: '0',
                data: {
                    name: '曹乐',
                    lastYearSale: 45200.75,
                    thisYearSale: 52800.40,
                    diff: parseFloat((45200.75 - 52800.40).toFixed(2))
                }
            },
            {
                key: '1',
                data: {
                    name: '机构业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '1-0',
                        data: {
                            name: '陆健',
                            lastYearSale: 89600.30,
                            thisYearSale: 75300.65,
                            diff: parseFloat((89600.30 - 75300.65).toFixed(2))
                        }
                    },
                    {
                        key: '1-1',
                        data: {
                            name: '金胜尧',
                            lastYearSale: 125800.90,
                            thisYearSale: 142600.20,
                            diff: parseFloat((125800.90 - 142600.20).toFixed(2))
                        }
                    },
                    {
                        key: '1-2',
                        data: {
                            name: '王子淳',
                            lastYearSale: 36400.15,
                            thisYearSale: 39800.80,
                            diff: parseFloat((36400.15 - 39800.80).toFixed(2))
                        }
                    }
                ]
            },
            {
                key: '2',
                data: {
                    name: '经纪业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '2-0',
                        data: {
                            name: '温贵忠',
                            lastYearSale: 215700.50,
                            thisYearSale: 198300.75,
                            diff: parseFloat((215700.50 - 198300.75).toFixed(2))
                        }
                    },
                ]
            },
            {
                key: '3',
                data: {
                    name: '马帅帅',
                    lastYearSale: 68900.40,
                    thisYearSale: 82500.30,
                    diff: parseFloat((68900.40 - 82500.30).toFixed(2))
                }
            },
            {
                key: '4',
                data: {
                    name: '邹军',
                    lastYearSale: 156200.85,
                    thisYearSale: 149700.60,
                    diff: parseFloat((156200.85 - 149700.60).toFixed(2))
                }
            },

            {
                key: '5',
                data: {
                    name: '潘超',
                    lastYearSale: 94300.25,
                    thisYearSale: 108600.50,
                    diff: parseFloat((94300.25 - 108600.50).toFixed(2))
                }
            },
        ];
    } else if (selectedCity.value?.code === 'AST') {
        optionData = [
            {
                key: '0',
                data: {
                    name: '曹乐',
                    lastYearSale: 67800.25, thisYearSale: 72500.60, diff: parseFloat((67800.25 - 72500.60).toFixed(2))
                }
            },
            {
                key: '1',
                data: {
                    name: '机构业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '1-0',
                        data: {
                            lastYearSale: 135200.80, thisYearSale: 129800.45, diff: parseFloat((135200.80 - 129800.45).toFixed(2))
                        }
                    },
                    {
                        key: '1-1',
                        data: {
                            name: '金胜尧',
                            lastYearSale: 52300.70, thisYearSale: 68900.90, diff: parseFloat((52300.70 - 68900.90).toFixed(2))
                        }
                    },
                    {
                        key: '1-2',
                        data: {
                            name: '王子淳',
                            lastYearSale: 218500.30, thisYearSale: 205700.65, diff: parseFloat((218500.30 - 205700.65).toFixed(2))
                        }
                    }
                ]
            },
            {
                key: '2',
                data: {
                    name: '经纪业务部',
                    lastYearSale: 0,
                    thisYearSale: 0,
                    diff: 0
                },
                children: [
                    {
                        key: '2-0',
                        data: {
                            name: '温贵忠',
                            lastYearSale: 89600.50, thisYearSale: 94200.20, diff: parseFloat((89600.50 - 94200.20).toFixed(2))
                        }
                    },
                ]
            },
            {
                key: '3',
                data: {
                    name: '马帅帅',
                    lastYearSale: 342700.85, thisYearSale: 368900.50, diff: parseFloat((342700.85 - 368900.50).toFixed(2))
                }
            },
            {
                key: '4',
                data: {
                    name: '邹军',
                    lastYearSale: 76400.15, thisYearSale: 71200.80, diff: parseFloat((76400.15 - 71200.80).toFixed(2))
                }
            },

            {
                key: '5',
                data: {
                    name: '潘超',
                    lastYearSale: 189300.60, thisYearSale: 201500.30, diff: parseFloat((189300.60 - 201500.30).toFixed(2))
                }
            },
        ];
    }

    return optionData;
});







</script>
<style>
@layer reset, primevue, custom;

@layer custom {
    .main-periodCompareDataBoard-container {
        /* background-color: bisque; */
        /* height: 800px; */
    }

    .main-periodCompareDataBoard-container .card-header {
        display: flex;
        flex-direction: row;
        align-items: center;

        width: 100%;
    }

    .main-periodCompareDataBoard-container .card-header .left {
        /* background-color: antiquewhite; */

        width: 40%;

        font-size: 1.3rem;
        font-weight: bold;
        letter-spacing: 0.08rem;

        padding: 0.4rem;
        padding-left: 0.7rem;
    }

    .main-periodCompareDataBoard-container .card-header .right {
        /* background-color: rgb(209, 165, 106); */

        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 0.8rem;

        width: 60%;

        padding: 0.4rem;
    }

    .main-periodCompareDataBoard-container .card-header .right .download-button {
        background-color: #202020;
        border: none;
    }

    .main-periodCompareDataBoard-container .options-panel {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .main-periodCompareDataBoard-container .data-panel {
        /* padding:1rem */
    }

    .main-periodCompareDataBoard-container .options-panel .info {
        color: #6a6b6e;
    }

    .main-periodCompareDataBoard-container .options-panel .options-panel-content {
        /* background-color: antiquewhite; */
        width: 100%;
        height: 100%;
    }

    .main-periodCompareDataBoard-container .options-panel .options-panel-content .listbox {
        height: 100%;
        border-left: none;
        border-right: none;
        /* display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center; */
    }


    /* 差值列的样式 */
    .red-text {
        color: #e74c3c;
        font-weight: bold;
    }

    .green-text {
        color: #27ae60;
        font-weight: bold;
    }

}
</style>
